<Window x:Class="DriverManagementSystem.Views.PrintPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة الطباعة - محضر استخراج عروض الأسعار" 
        Height="900" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        WindowState="Maximized">

    <Grid Background="#f5f5f5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Toolbar -->
        <Border Grid.Row="0" Background="#2c3e50" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🖨️" FontSize="28" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBlock Text="معاينة الطباعة - محضر استخراج عروض الأسعار" 
                             FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🖨️ طباعة" 
                            Click="PrintButton_Click"
                            Background="#27ae60" 
                            Foreground="White" 
                            Padding="20,10" 
                            FontSize="14"
                            FontWeight="Bold"
                            Margin="0,0,10,0"/>
                    <Button Content="📁 حفظ PDF" 
                            Click="SavePdfButton_Click"
                            Background="#8e44ad" 
                            Foreground="White" 
                            Padding="20,10" 
                            FontSize="14"
                            FontWeight="Bold"
                            Margin="0,0,10,0"/>
                    <Button Content="❌ إغلاق" 
                            Click="CloseButton_Click"
                            Background="#e74c3c" 
                            Foreground="White" 
                            Padding="20,10" 
                            FontSize="14"
                            FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Print Preview Area -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      Background="#e8e8e8"
                      Padding="10">

            <!-- A4 Paper Container - Full width preview -->
            <!-- A4 proportions optimized for full visibility -->
            <Border x:Name="PrintableArea"
                    Background="White"
                    BorderBrush="#cccccc"
                    BorderThickness="2"
                    Width="800"
                    Height="1100"
                    Margin="20"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Top">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="6" Opacity="0.3"/>
                </Border.Effect>

                <!-- Report Content with full visibility -->
                <ContentPresenter x:Name="ReportContentPresenter"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                Margin="10"/>
            </Border>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#34495e" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="👁️" FontSize="16" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="معاينة مصغرة - الطباعة ستكون بالحجم الكامل"
                             FontSize="14" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="📐 معاينة: 75% | طباعة: A4 كامل (210×297mm)"
                             FontSize="12" Foreground="#bdc3c7" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <TextBlock Text="🖨️ 300 DPI - جودة طباعة عالية"
                             FontSize="12" Foreground="#bdc3c7" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
