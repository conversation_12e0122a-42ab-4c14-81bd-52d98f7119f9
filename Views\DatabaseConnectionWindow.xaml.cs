using System;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Views
{
    public partial class DatabaseConnectionWindow : Window
    {
        public DatabaseConnectionWindow()
        {
            InitializeComponent();
            LoadExistingSettings();
            SetupEventHandlers();
        }

        private void LoadExistingSettings()
        {
            try
            {
                var settings = DatabaseConfig.LoadSettings();

                // نوع قاعدة البيانات
                ServerDatabaseRadio.IsChecked = !settings.UseLocalDatabase;
                LocalDatabaseRadio.IsChecked = settings.UseLocalDatabase;

                // إعدادات الخادم
                ServerNameTextBox.Text = settings.ServerName;
                DatabaseNameTextBox.Text = settings.DatabaseName;
                WindowsAuthRadio.IsChecked = settings.UseWindowsAuth;
                SqlAuthRadio.IsChecked = !settings.UseWindowsAuth;
                UsernameTextBox.Text = settings.Username;

                // إعدادات قاعدة البيانات المحلية
                LocalDatabaseNameTextBox.Text = settings.DatabaseName;

                UpdateDatabaseTypePanel();
                UpdateAuthenticationPanel();
                UpdateLocalDatabasePath();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الإعدادات: {ex.Message}");
            }
        }

        private void SetupEventHandlers()
        {
            WindowsAuthRadio.Checked += (s, e) => UpdateAuthenticationPanel();
            SqlAuthRadio.Checked += (s, e) => UpdateAuthenticationPanel();
            LocalDatabaseNameTextBox.TextChanged += (s, e) => UpdateLocalDatabasePath();
        }

        private void UpdateAuthenticationPanel()
        {
            SqlAuthPanel.Visibility = SqlAuthRadio.IsChecked == true ? Visibility.Visible : Visibility.Collapsed;
        }

        private void UpdateDatabaseTypePanel()
        {
            if (LocalDatabaseRadio.IsChecked == true)
            {
                ServerSettingsPanel.Visibility = Visibility.Collapsed;
                LocalDatabasePanel.Visibility = Visibility.Visible;
                LocalDatabaseInfo.Visibility = Visibility.Visible;
            }
            else
            {
                ServerSettingsPanel.Visibility = Visibility.Visible;
                LocalDatabasePanel.Visibility = Visibility.Collapsed;
                LocalDatabaseInfo.Visibility = Visibility.Collapsed;
            }
        }

        private void UpdateLocalDatabasePath()
        {
            try
            {
                var dataFolder = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbName = string.IsNullOrWhiteSpace(LocalDatabaseNameTextBox.Text) ? "SFDSYS" : LocalDatabaseNameTextBox.Text;
                var dbPath = System.IO.Path.Combine(dataFolder, $"{dbName}.mdf");
                LocalDatabasePathTextBox.Text = dbPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مسار قاعدة البيانات: {ex.Message}");
            }
        }

        private void ServerDatabaseRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateDatabaseTypePanel();
        }

        private void LocalDatabaseRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateDatabaseTypePanel();
            UpdateLocalDatabasePath();
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TestConnectionButton.IsEnabled = false;
                TestConnectionButton.Content = "🔄 جاري الاختبار...";

                var settings = GetCurrentSettings();
                var connectionString = DatabaseConfig.BuildConnectionString(settings);
                
                bool canConnect = await DatabaseConfig.TestConnectionAsync(connectionString);

                if (canConnect)
                {
                    ShowStatus("✅ تم الاتصال بنجاح!", "#4CAF50", "#E8F5E8");
                }
                else
                {
                    ShowStatus("❌ فشل الاتصال! تحقق من الإعدادات", "#F44336", "#FFEBEE");
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"❌ خطأ: {ex.Message}", "#F44336", "#FFEBEE");
            }
            finally
            {
                TestConnectionButton.IsEnabled = true;
                TestConnectionButton.Content = "🔍 اختبار الاتصال";
            }
        }

        private async void CreateDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CreateDatabaseButton.IsEnabled = false;
                CreateDatabaseButton.Content = "🔄 جاري الإنشاء...";

                // حفظ الإعدادات أولاً
                var settings = GetCurrentSettings();
                DatabaseConfig.SaveSettings(settings);

                // إنشاء قاعدة البيانات
                bool success = await DatabaseConfig.CreateDatabaseIfNotExists();

                if (success)
                {
                    ShowStatus("✅ تم إنشاء قاعدة البيانات بنجاح!", "#4CAF50", "#E8F5E8");
                    
                    // اختبار الاتصال بعد الإنشاء
                    var connectionString = DatabaseConfig.BuildConnectionString(settings);
                    bool canConnect = await DatabaseConfig.TestConnectionAsync(connectionString);
                    
                    if (canConnect)
                    {
                        ShowStatus("✅ قاعدة البيانات جاهزة للاستخدام!", "#4CAF50", "#E8F5E8");
                    }
                }
                else
                {
                    ShowStatus("❌ فشل في إنشاء قاعدة البيانات!", "#F44336", "#FFEBEE");
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"❌ خطأ: {ex.Message}", "#F44336", "#FFEBEE");
            }
            finally
            {
                CreateDatabaseButton.IsEnabled = true;
                CreateDatabaseButton.Content = "🏗️ إنشاء قاعدة البيانات";
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveButton.IsEnabled = false;
                SaveButton.Content = "🔄 جاري الحفظ...";

                var settings = GetCurrentSettings();
                
                // اختبار الاتصال أولاً
                var connectionString = DatabaseConfig.BuildConnectionString(settings);
                bool canConnect = await DatabaseConfig.TestConnectionAsync(connectionString);

                if (!canConnect)
                {
                    var result = MessageBox.Show(
                        "لا يمكن الاتصال بقاعدة البيانات بالإعدادات الحالية.\nهل تريد إنشاء قاعدة البيانات؟",
                        "تأكيد الإنشاء",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        bool created = await DatabaseConfig.CreateDatabaseIfNotExists();
                        if (!created)
                        {
                            MessageBox.Show("فشل في إنشاء قاعدة البيانات!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }
                    }
                    else
                    {
                        return;
                    }
                }

                // حفظ الإعدادات
                DatabaseConfig.SaveSettings(settings);
                ShowStatus("✅ تم حفظ الإعدادات بنجاح!", "#4CAF50", "#E8F5E8");

                // إغلاق النافذة بنجاح
                await Task.Delay(1000);
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowStatus($"❌ خطأ: {ex.Message}", "#F44336", "#FFEBEE");
            }
            finally
            {
                SaveButton.IsEnabled = true;
                SaveButton.Content = "💾 حفظ الإعدادات";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private DatabaseConfig.DatabaseSettings GetCurrentSettings()
        {
            var settings = new DatabaseConfig.DatabaseSettings();

            if (LocalDatabaseRadio.IsChecked == true)
            {
                // قاعدة بيانات محلية
                settings.UseLocalDatabase = true;
                settings.DatabaseName = LocalDatabaseNameTextBox.Text.Trim();
                settings.LocalDatabasePath = LocalDatabasePathTextBox.Text.Trim();
                settings.UseWindowsAuth = true; // LocalDB يستخدم Windows Auth دائماً
            }
            else
            {
                // قاعدة بيانات خادم
                settings.UseLocalDatabase = false;
                settings.ServerName = ServerNameTextBox.Text.Trim();
                settings.DatabaseName = DatabaseNameTextBox.Text.Trim();
                settings.UseWindowsAuth = WindowsAuthRadio.IsChecked == true;
                settings.Username = UsernameTextBox.Text.Trim();
                settings.Password = PasswordBox.Password;
            }

            return settings;
        }

        private void ShowStatus(string message, string borderColor, string backgroundColor)
        {
            StatusText.Text = message;
            StatusBorder.BorderBrush = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(borderColor));
            StatusBorder.Background = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(backgroundColor));
            StatusBorder.Visibility = Visibility.Visible;
        }
    }
}
