<Window x:Class="DriverManagementSystem.Views.SimpleAssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="700"
        Width="900"
        WindowStartupLocation="CenterScreen"
        Background="#F5F7FA"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Text="التكليف الرسمي"
                         FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Text=" - زيارة ميدانية"
                         FontSize="16" Foreground="#BDC3C7" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Border Background="White" BorderBrush="#E1E8ED" BorderThickness="1"
                    CornerRadius="8" Padding="30" MaxWidth="800">
                <StackPanel>
                    
                    <!-- عنوان التكليف -->
                    <Border Background="#1e3c72" CornerRadius="8" Padding="20" Margin="0,0,0,30">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📋 تكليف رسمي 📋" FontSize="24" FontWeight="Bold"
                                     Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Text="تكليف رقم 001/2025" FontSize="14"
                                     Foreground="#E8F4FD" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- معلومات الزيارة -->
                    <Border Background="#F8F9FA" BorderBrush="#E1E8ED" BorderThickness="1"
                            CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="📋 معلومات المهمة" FontSize="16" FontWeight="Bold"
                                     Foreground="#2C3E50" Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1" Margin="0,0,0,10">
                                <TextBlock Text="🏗️ المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="مشروع تطوير المجتمعات الريفية" TextWrapping="Wrap"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Margin="0,0,0,10">
                                <TextBlock Text="⚡ النشاط:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="زيارة ميدانية لمتابعة تنفيذ المشاريع" TextWrapping="Wrap"/>
                            </StackPanel>

                            <StackPanel Grid.Row="3">
                                <TextBlock Text="📅 تاريخ التحرك:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="من 01/01/2025 إلى 03/01/2025 (3 أيام)"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- القائمون بالزيارة -->
                    <Border Background="White" BorderBrush="#1e3c72" BorderThickness="2"
                            CornerRadius="8" Margin="0,0,0,20">
                        <StackPanel>
                            <Border Background="#1e3c72" CornerRadius="6,6,0,0" Padding="15">
                                <TextBlock Text="👥 القائمون بالزيارة" FontSize="16" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <StackPanel Margin="15">
                                <!-- القائم الأول -->
                                <Border Background="#F8F9FA" BorderBrush="#E1E8ED" BorderThickness="1"
                                        CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="40"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="120"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#1e3c72" CornerRadius="20"
                                                Width="30" Height="30">
                                            <TextBlock Text="1" FontSize="12" FontWeight="Bold"
                                                     Foreground="White" HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" Margin="10,0">
                                            <TextBlock Text="أحمد محمد الشامي" FontSize="14" FontWeight="Bold"/>
                                            <TextBlock Text="مدير المشاريع" FontSize="12" Foreground="#666"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="📞 الهاتف" FontSize="10" Foreground="#666"/>
                                            <TextBlock Text="777123456" FontSize="12"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="3">
                                            <TextBlock Text="🆔 البطاقة" FontSize="10" Foreground="#666"/>
                                            <TextBlock Text="12345678" FontSize="12"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>

                                <!-- القائم الثاني -->
                                <Border Background="#F8F9FA" BorderBrush="#E1E8ED" BorderThickness="1"
                                        CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="40"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="120"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#1e3c72" CornerRadius="20"
                                                Width="30" Height="30">
                                            <TextBlock Text="2" FontSize="12" FontWeight="Bold"
                                                     Foreground="White" HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" Margin="10,0">
                                            <TextBlock Text="فاطمة علي الحميري" FontSize="14" FontWeight="Bold"/>
                                            <TextBlock Text="مسؤولة المتابعة" FontSize="12" Foreground="#666"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="📞 الهاتف" FontSize="10" Foreground="#666"/>
                                            <TextBlock Text="777654321" FontSize="12"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="3">
                                            <TextBlock Text="🆔 البطاقة" FontSize="10" Foreground="#666"/>
                                            <TextBlock Text="87654321" FontSize="12"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- السائق -->
                    <Border Background="White" BorderBrush="#FF9800" BorderThickness="2"
                            CornerRadius="8" Margin="0,0,0,20">
                        <StackPanel>
                            <Border Background="#FF9800" CornerRadius="6,6,0,0" Padding="15">
                                <TextBlock Text="🚗 السائق المكلف" FontSize="16" FontWeight="Bold"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>
                            
                            <Border Background="#FFF3E0" Padding="15" Margin="15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="120"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="👤 اسم السائق" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="أحمد محمد الشامي" FontSize="14" FontWeight="Bold"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="📞 الهاتف" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="777123456" FontSize="12"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="🚙 نوع السيارة" FontSize="10" Foreground="#666"/>
                                        <TextBlock Text="تويوتا هايلكس" FontSize="12" FontWeight="Bold"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- النص الختامي -->
                    <Border Background="#F8F9FA" BorderBrush="#1e3c72" BorderThickness="1"
                            CornerRadius="8" Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="وعليه تكرموا بالتعاون مع المذكورين أعلاه لما فيه المصلحة العامة وتحقيق أهداف المؤسسة."
                                     FontSize="14" TextAlignment="Justify" TextWrapping="Wrap" Margin="0,0,0,10"/>
                            <TextBlock Text="وشكراً لحسن تعاونكم" FontSize="14" FontWeight="Bold"
                                     Foreground="#1e3c72" HorizontalAlignment="Right"/>
                        </StackPanel>
                    </Border>

                    <!-- التوقيع -->
                    <Border Background="White" BorderBrush="#1e3c72" BorderThickness="1"
                            CornerRadius="8" Padding="20" HorizontalAlignment="Center">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="✍️ التوقيع" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Text="مدير الفرع" FontSize="14" FontWeight="Bold"
                                     Foreground="#1e3c72" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                            <Rectangle Height="1" Width="120" Fill="#1e3c72" Margin="0,10,0,10"/>
                            <TextBlock Text="م/محمد محمد الديلمي" FontSize="12"
                                     Foreground="#2a5298" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E1E8ED" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🖨️ طباعة"
                        Click="PrintButton_Click"
                        Background="#28A745" Foreground="White"
                        Padding="20,10" FontWeight="Bold" FontSize="14"
                        BorderThickness="0" Margin="0,0,15,0" Cursor="Hand"/>

                <Button Content="❌ إغلاق"
                        Click="CloseButton_Click"
                        Background="#DC3545" Foreground="White"
                        Padding="20,10" FontWeight="Bold" FontSize="14"
                        BorderThickness="0" Cursor="Hand"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
