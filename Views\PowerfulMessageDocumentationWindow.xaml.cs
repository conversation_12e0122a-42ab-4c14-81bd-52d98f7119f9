using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using DriverManagementSystem.Models;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    public partial class PowerfulMessageDocumentationWindow : Window
    {
        public PowerfulMessageDocumentationWindow()
        {
            InitializeComponent();
            // إنشاء ViewModel افتراضي بدون زيارة محددة
            DataContext = new PowerfulMessageDocumentationViewModel(null);
        }

        public PowerfulMessageDocumentationWindow(FieldVisit selectedVisit) : this()
        {
            DataContext = new PowerfulMessageDocumentationViewModel(selectedVisit);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }



        private void Image_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // هذا المعالج لم يعد مستخدماً - تم استبداله بالمعاينة الجانبية
        }

        private void SideThumbnail_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.DataContext is string imagePath)
            {
                try
                {
                    // عرض الصورة في المعاينة الجانبية الكبيرة
                    LargePreviewImage.Source = new BitmapImage(new Uri(imagePath));
                    LargePreviewName.Text = System.IO.Path.GetFileName(imagePath);
                    LargePreviewBorder.Visibility = Visibility.Visible;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في عرض الصورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CloseImagePreview_Click(object sender, RoutedEventArgs e)
        {
            ImagePreviewOverlay.Visibility = Visibility.Collapsed;
        }
    }
}
