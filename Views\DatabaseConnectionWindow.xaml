<Window x:Class="DriverManagementSystem.Views.DatabaseConnectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🔧 إعداد قاعدة البيانات - SQL Server"
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style TargetType="Label">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#4A90E2" CornerRadius="8" Padding="15" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="🗄️ إعداد اتصال قاعدة البيانات"
                          FontSize="18" FontWeight="Bold"
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="SQL Server Database Configuration"
                          FontSize="12"
                          Foreground="#E8F4FD" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Database Type -->
                <Label Content="📁 نوع قاعدة البيانات:"/>
                <StackPanel Orientation="Horizontal" Margin="5">
                    <RadioButton x:Name="ServerDatabaseRadio" Content="قاعدة بيانات خادم (Server Database)"
                               IsChecked="True" Margin="0,0,20,0" FontSize="14" Checked="ServerDatabaseRadio_Checked"/>
                    <RadioButton x:Name="LocalDatabaseRadio" Content="قاعدة بيانات محلية (Local Database)"
                               FontSize="14" Checked="LocalDatabaseRadio_Checked"/>
                </StackPanel>

                <!-- Local Database Info -->
                <Border x:Name="LocalDatabaseInfo" Background="#E8F5E8" BorderBrush="#4CAF50"
                       BorderThickness="1" CornerRadius="5" Padding="10" Margin="5"
                       Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="💡 قاعدة البيانات المحلية:" FontWeight="Bold" Foreground="#2E7D32"/>
                        <TextBlock Text="• يتم حفظها في مجلد Data داخل النظام" Margin="10,5,0,0"/>
                        <TextBlock Text="• يمكن نسخ النظام كاملاً إلى أي جهاز آخر" Margin="10,0,0,0"/>
                        <TextBlock Text="• لا تحتاج إعداد خادم SQL Server منفصل" Margin="10,0,0,5"/>
                    </StackPanel>
                </Border>

                <!-- Server Settings Panel -->
                <StackPanel x:Name="ServerSettingsPanel">
                    <!-- Server Name -->
                    <Label Content="🖥️ اسم الخادم (Server Name):"/>
                    <TextBox x:Name="ServerNameTextBox" Text="localhost"/>

                    <!-- Database Name -->
                    <Label Content="🗃️ اسم قاعدة البيانات (Database Name):"/>
                    <TextBox x:Name="DatabaseNameTextBox" Text="SFDSYS"/>

                    <!-- Authentication Type -->
                    <Label Content="🔐 نوع المصادقة (Authentication):"/>
                    <StackPanel Orientation="Horizontal" Margin="5">
                        <RadioButton x:Name="WindowsAuthRadio" Content="Windows Authentication"
                                   IsChecked="True" Margin="0,0,20,0" FontSize="14"/>
                        <RadioButton x:Name="SqlAuthRadio" Content="SQL Server Authentication"
                                   FontSize="14"/>
                    </StackPanel>
                </StackPanel>

                <!-- Local Database Settings Panel -->
                <StackPanel x:Name="LocalDatabasePanel" Visibility="Collapsed">
                    <Label Content="🗃️ اسم قاعدة البيانات المحلية:"/>
                    <TextBox x:Name="LocalDatabaseNameTextBox" Text="SFDSYS"/>

                    <Label Content="📁 مسار الحفظ:"/>
                    <TextBox x:Name="LocalDatabasePathTextBox" IsReadOnly="True" Background="#F5F5F5"/>
                </StackPanel>

                <!-- SQL Authentication Details -->
                <StackPanel x:Name="SqlAuthPanel" Visibility="Collapsed">
                    <Label Content="👤 اسم المستخدم (Username):"/>
                    <TextBox x:Name="UsernameTextBox" Text="sa"/>

                    <Label Content="🔑 كلمة المرور (Password):"/>
                    <PasswordBox x:Name="PasswordBox"/>
                </StackPanel>

                <!-- Connection Status -->
                <Border x:Name="StatusBorder" Background="#E8F5E8" BorderBrush="#4CAF50"
                       BorderThickness="1" CornerRadius="5" Padding="10" Margin="5"
                       Visibility="Collapsed">
                    <TextBlock x:Name="StatusText" FontWeight="Bold" Foreground="#2E7D32"/>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="TestConnectionButton" Content="🔍 اختبار الاتصال"
                   Background="#FF9800" Foreground="White" Click="TestConnectionButton_Click"/>

            <Button x:Name="CreateDatabaseButton" Content="🏗️ إنشاء قاعدة البيانات"
                   Background="#4CAF50" Foreground="White" Click="CreateDatabaseButton_Click"/>

            <Button x:Name="SaveButton" Content="💾 حفظ الإعدادات"
                   Background="#2196F3" Foreground="White" Click="SaveButton_Click"/>

            <Button x:Name="CancelButton" Content="❌ إلغاء"
                   Background="#F44336" Foreground="White" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>