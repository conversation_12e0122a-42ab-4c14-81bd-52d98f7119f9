using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using DriverManagementSystem.Views;

namespace DriverManagementSystem.ViewModels
{
    public class MessageDocumentationViewModel : BindableBase
    {
        private readonly ApplicationDbContext _context;
        private MessageDocumentation _documentation;
        private FieldVisit _selectedVisit;

        public MessageDocumentationViewModel(FieldVisit selectedVisit)
        {
            _context = new ApplicationDbContext();
            _selectedVisit = selectedVisit;
            
            InitializeDocumentation();
            InitializeCommands();
        }

        #region Properties

        public MessageDocumentation Documentation
        {
            get => _documentation;
            set => SetProperty(ref _documentation, value);
        }

        public FieldVisit SelectedVisit
        {
            get => _selectedVisit;
            set => SetProperty(ref _selectedVisit, value);
        }

        public ObservableCollection<MessageAttachment> Attachments { get; set; } = new ObservableCollection<MessageAttachment>();

        private string _reportNumber;
        public string ReportNumber
        {
            get => _reportNumber;
            set => SetProperty(ref _reportNumber, value);
        }

        private string _firstOfficer;
        public string FirstOfficer
        {
            get => _firstOfficer;
            set => SetProperty(ref _firstOfficer, value);
        }

        private string _secondOfficer;
        public string SecondOfficer
        {
            get => _secondOfficer;
            set => SetProperty(ref _secondOfficer, value);
        }

        private string _thirdOfficer;
        public string ThirdOfficer
        {
            get => _thirdOfficer;
            set => SetProperty(ref _thirdOfficer, value);
        }

        private string _notes;
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        #endregion

        #region Commands

        public ICommand AddAttachmentCommand { get; private set; }
        public ICommand RemoveAttachmentCommand { get; private set; }
        public ICommand SaveDocumentationCommand { get; private set; }
        public ICommand PreviewReportCommand { get; private set; }
        public ICommand PrintReportCommand { get; private set; }

        #endregion

        private void InitializeDocumentation()
        {
            Documentation = new MessageDocumentation
            {
                VisitNumber = _selectedVisit?.VisitNumber ?? "",
                DocumentationDate = DateTime.Now,
                ReportNumber = GenerateReportNumber()
            };

            ReportNumber = Documentation.ReportNumber;
        }

        private void InitializeCommands()
        {
            AddAttachmentCommand = new DelegateCommand(AddAttachment);
            RemoveAttachmentCommand = new DelegateCommand<MessageAttachment>(RemoveAttachment);
            SaveDocumentationCommand = new DelegateCommand(SaveDocumentation);
            PreviewReportCommand = new DelegateCommand(PreviewReport);
            PrintReportCommand = new DelegateCommand(PrintReport);
        }

        private string GenerateReportNumber()
        {
            var date = DateTime.Now;
            return $"{date.Year}{date.Month:D2}{date.Day:D2}-{_selectedVisit?.VisitNumber ?? "001"}";
        }

        private void AddAttachment()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار مرفق",
                    Filter = "جميع الملفات (*.*)|*.*|الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|مستندات PDF (*.pdf)|*.pdf",
                    Multiselect = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    foreach (var fileName in openFileDialog.FileNames)
                    {
                        var fileInfo = new FileInfo(fileName);
                        var attachment = new MessageAttachment
                        {
                            FileName = fileInfo.Name,
                            FilePath = fileName,
                            FileType = fileInfo.Extension,
                            FileSize = fileInfo.Length,
                            Description = $"مرفق {Attachments.Count + 1}"
                        };

                        Attachments.Add(attachment);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المرفق: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveAttachment(MessageAttachment attachment)
        {
            if (attachment != null)
            {
                Attachments.Remove(attachment);
            }
        }

        private void SaveDocumentation()
        {
            try
            {
                Documentation.ReportNumber = ReportNumber;
                Documentation.FirstOfficer = FirstOfficer;
                Documentation.SecondOfficer = SecondOfficer;
                Documentation.ThirdOfficer = ThirdOfficer;
                Documentation.Notes = Notes;
                Documentation.LastModified = DateTime.Now;

                // حفظ المرفقات
                Documentation.Attachments.Clear();
                foreach (var attachment in Attachments)
                {
                    Documentation.Attachments.Add(attachment);
                }

                _context.MessageDocumentations.Add(Documentation);
                _context.SaveChanges();

                MessageBox.Show("تم حفظ التوثيق بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التوثيق: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviewReport()
        {
            try
            {
                // تم إزالة MessageDocumentationReportWindow - استخدم النافذة البديلة
                var imagePaths = Attachments.Where(a => a.FileType != null &&
                    (a.FileType.ToLower().Contains("image") ||
                     a.FileType.ToLower().Contains("jpg") ||
                     a.FileType.ToLower().Contains("png") ||
                     a.FileType.ToLower().Contains("jpeg")))
                    .Select(a => a.FilePath).ToList();
                var previewWindow = new MessageDocumentationImagesReportWindow(Documentation, imagePaths);
                previewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintReport()
        {
            try
            {
                // سيتم تنفيذها لاحقاً
                MessageBox.Show("سيتم تنفيذ الطباعة قريباً", "قيد التطوير", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
